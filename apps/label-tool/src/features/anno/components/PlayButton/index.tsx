import { Input<PERSON><PERSON>ber, Tooltip } from 'antd';
import { pick } from 'lodash';
import { useEffect, useRef, useState } from 'react';
import { RiPlayCircleLine, RiPlayFill, RiSkipBackFill, RiSkipForwardFill, RiStopFill } from 'react-icons/ri';

import { MenuButton, PopoverWithTooltip } from '@/components';
import type { ToolType } from '@/utils/tool';

import { useContextStore } from '../../stores';

import styles from './styles.module.css';

interface PlayButtonProps {}
type PlayerState = 'forward' | 'backward' | 'stop';

const PlayButtonContent: React.FC<PlayButtonProps> = () => {
  const [playerState, setPlayerState] = useState<PlayerState>('stop');

  // 单位：秒
  const [timeInterval, setTime] = useState<number>(0.5);
  const preTool = useRef<ToolType | undefined>();

  const { currentElementIndex, currentTool, elementsState, posePoints, labelStage, setElementIndex, setCurrentTool } =
    useContextStore((state) =>
      pick(state, [
        'elementsState',
        'currentElementIndex',
        'currentTool',
        'setElementIndex',
        'setCurrentTool',
        'posePoints',
        'labelStage',
      ])
    );

  const elementIndexRef = useRef<number>(currentElementIndex);

  const updateElementPose = (i: number) => {
    setElementIndex(i);
    labelStage?.currentSight?.setCameraToward(...posePoints[i]);
  };

  const startPlayer = (index: number, type: Extract<PlayerState, 'forward' | 'backward'>) => {
    let i = index;

    return () => {
      if (i !== elementIndexRef.current) i = elementIndexRef.current;
      if (type === 'forward') {
        i++;
        if (i === elementsState.length) {
          setPlayerState('stop');
          return;
        }
        updateElementPose(i);
      } else {
        i--;
        if (i < 0) {
          setPlayerState('stop');
          return;
        }
        updateElementPose(i);
      }
    };
  };

  useEffect(() => {
    if (playerState === 'stop') {
      return;
    }
    if (currentTool !== 'select') {
      preTool.current = currentTool;
      setCurrentTool('select');
    }
    const interval = setInterval(startPlayer(currentElementIndex, playerState), timeInterval * 1000);

    return () => {
      clearInterval(interval);
      if (preTool.current) {
        setCurrentTool(preTool.current);
        preTool.current = undefined;
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [playerState, timeInterval]);

  useEffect(() => {
    elementIndexRef.current = currentElementIndex;
  }, [currentElementIndex]);

  return (
    <div className={styles['play-button']}>
      <Tooltip title={'回到第一帧'} placement="top">
        <div className={styles.icon} onClick={() => updateElementPose(0)} aria-disabled={currentElementIndex === 0}>
          <RiSkipBackFill fontSize={16} />
        </div>
      </Tooltip>
      {playerState === 'forward' ? (
        <Tooltip title={'暂停播放'} placement="top">
          <div className={styles.icon} onClick={() => setPlayerState('stop')}>
            <RiStopFill fontSize={16} />
          </div>
        </Tooltip>
      ) : (
        <Tooltip title={'正向播放'} placement="top">
          <div
            className={styles.icon}
            onClick={() => currentElementIndex < elementsState.length - 1 && setPlayerState('forward')}
            aria-disabled={currentElementIndex === elementsState.length - 1}
          >
            <RiPlayFill fontSize={16} />
          </div>
        </Tooltip>
      )}
      {playerState === 'backward' ? (
        <Tooltip title={'暂停播放'} placement="top">
          <div className={styles.icon} onClick={() => setPlayerState('stop')}>
            <RiStopFill fontSize={16} />
          </div>
        </Tooltip>
      ) : (
        <Tooltip title={'反向播放'} placement="top">
          <div
            className={styles.icon}
            onClick={() => currentElementIndex > 0 && setPlayerState('backward')}
            aria-disabled={currentElementIndex === 0}
          >
            <RiPlayFill fontSize={16} className={styles['play-back']} />
          </div>
        </Tooltip>
      )}
      <Tooltip title={'回到最后一帧'} placement="top">
        <div
          className={styles.icon}
          onClick={() => updateElementPose(elementsState.length - 1)}
          aria-disabled={currentElementIndex === elementsState.length - 1}
        >
          <RiSkipForwardFill fontSize={16} />
        </div>
      </Tooltip>
      帧延迟
      <InputNumber
        type="number"
        size="small"
        className={styles['play-interval-time']}
        value={timeInterval}
        onChange={(val) => setTime(Number(val))}
        max={3}
        min={0.1}
        step={0.1}
        precision={1}
      />
      秒
    </div>
  );
};

export const PlayButton: React.FC<PlayButtonProps> = () => {
  return (
    <PopoverWithTooltip
      content={<PlayButtonContent />}
      placement="topRight"
      tooltipPlacement="top"
      tooltipTitle="帧播放"
    >
      <MenuButton>
        <RiPlayCircleLine fontSize={24} />
      </MenuButton>
    </PopoverWithTooltip>
  );
};
