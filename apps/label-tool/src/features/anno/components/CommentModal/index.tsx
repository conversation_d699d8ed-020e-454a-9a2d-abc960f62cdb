import { Button, Input, Modal, Select } from 'antd';
import { pick } from 'lodash';
import { useEffect, useMemo, useState } from 'react';
import { MathUtils } from 'three';
import { shallow } from 'zustand/shallow';

import { useContextStore, useStoreApi } from '@/features/anno/stores';
import { CommentFactory } from '@/utils/annos';

import styles from './styles.module.css';

const { TextArea } = Input;

export const CommentModal: React.FC<{}> = () => {
  const [state, setState] = useState<{
    isOpen: boolean;
    selectedClass?: string;
    selectedResult: string[];
    commentContent: string;
  }>({
    isOpen: false,
    selectedClass: undefined,
    selectedResult: [],
    commentContent: '',
  });

  const {
    lot,
    labelStage,
    dataElementIndex,
    currentPhase,
    commentModalData,
    commentMap,
    addComment,
    updateComment,
    deleteComment,
    createSnapshot,
    setCommentModal,
  } = useContextStore(
    (state) =>
      pick(state, [
        'lot',
        'labelStage',
        'dataElementIndex',
        'currentPhase',
        'commentModalData',
        'commentMap',
        'addComment',
        'updateComment',
        'deleteComment',
        'addResolveComment',
        'createSnapshot',
        'setCommentModal',
      ]),
    shallow
  );
  const store = useStoreApi();

  const selectOptions = useMemo(() => {
    if (!lot) return [];
    // 其他批注选项
    if (!lot.comment_reasons) return [];
    return lot?.comment_reasons.map((config) => ({
      label: config.class.display_name,
      value: config.class.name,
      options: config.reasons.map((reason) => ({
        label: reason.display_name,
        value: config.class.name + ':' + reason.name,
        key: config.class.name + ':' + reason.name,
        disabled: state.selectedClass && state.selectedClass !== config.class.name,
      })),
    }));
  }, [lot, state.selectedClass]);

  // 当前帧的批注数
  const newCommentCout = useMemo(() => {
    let count = 0;
    Object.values(commentMap).forEach((comment) => {
      if (!comment) return;
      if (comment.elem_idx !== dataElementIndex) return;
      const status = comment.status;
      if (comment && status && status === 'unresolved') count++;
    });
    return count;
  }, [commentMap, dataElementIndex]);

  useEffect(() => {
    if (commentModalData) {
      const { type } = commentModalData;

      switch (type) {
        case 'add':
          if (commentModalData.scope === 'element') {
            setState((prevState) => ({
              ...prevState,
              selectedClass: 'other',
              selectedResult: [`other:extra_information`],
              isOpen: true,
            }));
          } else {
            setState((prevState) => ({ ...prevState, isOpen: true }));
          }
          break;
        case 'edit':
        case 'missed':
          setState((prevState) => ({
            ...prevState,
            commentContent: commentModalData.content ?? '',
            selectedClass: commentModalData.reasons?.class ?? '',
            selectedResult: (commentModalData.reasons?.reasons ?? []).map(
              (reason) => `${commentModalData.reasons?.class}:${reason}`
            ),
            isOpen: true,
          }));
          break;
      }
    } else {
      setState((prevState) => ({ ...prevState, isOpen: false }));
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [commentModalData]);

  const onClose = () => {
    // 如果是漏标，且没有生成新的批注，则需要删除漏标的标注物
    if (commentModalData?.type === 'missed') {
      const comment = store.getState().commentMap[commentModalData.uuid];
      if (!comment) {
        labelStage?.currentSight?.removeObjectByName(commentModalData.uuid);
      } else {
        const commentObj = labelStage?.currentSight?.getObjectByName(commentModalData.uuid);
        commentObj && CommentFactory.updateObjectByComment(commentObj, comment);
      }
    }
    setCommentModal(null);
    setState({
      isOpen: false,
      commentContent: '',
      selectedClass: undefined,
      selectedResult: [],
    });
  };

  const onSelectChange = (value: string[]) => {
    setState((prevState) => {
      if (value.length > 0) {
        const [class_name] = value[0].split(':');
        return { ...prevState, selectedResult: value, selectedClass: class_name };
      } else {
        return { ...prevState, selectedResult: value, selectedClass: undefined };
      }
    });
  };

  const onContentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setState((prevState) => ({ ...prevState, commentContent: e.target.value.trim() }));
  };

  const handleDeleteComment = () => {
    if (!commentModalData || commentModalData.type !== 'edit') return;
    deleteComment(commentModalData.uuid);
    onClose();
    createSnapshot();
  };

  const onSubmit = () => {
    // 查看历史直接关闭
    if (!commentModalData || !state.selectedClass) {
      onClose();
      return;
    }

    const { type } = commentModalData;

    const normalInfo = {
      elem_idx: dataElementIndex,
      resolve_phase: -1,
      // 后端提交的接口更新，需要 commenter 信息，此处写死，后端会覆盖
      commenter: {
        name: '',
        uid: '',
        avatar: '',
      },
      add_phase: currentPhase?.number ?? 0,
      rd_idx: 0,
      created_at: new Date().toISOString(),
    };

    // 漏标
    if (type === 'missed') {
      const editInfo = {
        content: state.commentContent,
        reasons: {
          class: state.selectedClass,
          reasons: state.selectedResult.map((reason) => reason.split(':')[1]),
        },
        extra_info: { position: commentModalData.position },
      };

      if (!commentModalData.reasons) {
        addComment({
          ...normalInfo,
          scope: 'unspecified',
          obj_uuids: [],
          uuid: commentModalData.uuid,
          trackId: commentModalData.trackId,
          ...editInfo,
        });
      } else {
        const comment = commentMap[commentModalData.uuid];
        comment && updateComment({ ...comment, ...editInfo });
      }
    } else {
      const editInfo = {
        reasons: {
          class: state.selectedClass,
          reasons: state.selectedResult.map((reason) => reason.split(':')[1]),
        },
        content: state.commentContent,
      };

      if (type === 'add') {
        addComment({
          ...normalInfo,
          scope: commentModalData.scope,
          obj_uuids: commentModalData.obj_uuids,
          uuid: MathUtils.generateUUID(),
          trackId: commentModalData.trackId,
          ...editInfo,
        });
      } else {
        const comment = commentMap[commentModalData.uuid];
        comment && updateComment({ ...comment, ...editInfo });
      }
    }

    onClose();
    createSnapshot();
  };

  if (!commentModalData) return null;

  const { type } = commentModalData ?? {};

  const renderTitle = () => {
    if (!commentModalData) return '';
    const { type } = commentModalData;
    if (type === 'add' && commentModalData.scope === 'object') return '标注物批注';
    if (type === 'add' && commentModalData.scope === 'element') return '当前帧驳回';
    if (type === 'edit') return '修改批注';
    if (type === 'missed') return '指示器批注';
    return '';
  };

  const renderFooter = () => {
    return (
      <div className={styles.footer}>
        <Button
          type="primary"
          key="delete"
          danger
          ghost
          className={styles.delete}
          hidden={type !== 'edit'}
          onClick={handleDeleteComment}
        >
          删除
        </Button>
        <div>
          <Button className={styles.cancel} onClick={onClose}>
            取消
          </Button>
          <Button
            key="submit"
            type="primary"
            onClick={onSubmit}
            disabled={!state.selectedClass || (type === 'missed' && state.selectedResult.length === 0)}
          >
            确定
          </Button>
        </div>
      </div>
    );
  };

  const renderContent = () => {
    let differentPart = null;
    // 帧批注
    if (commentModalData.type === 'add' && commentModalData.scope === 'element') {
      differentPart = (
        <div className={styles['elem-info']}>
          此帧已被标注 <span>{newCommentCout}</span> 个错误
        </div>
      );
      // 漏标
    } else {
      differentPart = (
        <div className={styles['reason-form-class']}>
          <span className={styles['reason-form-class-label']}>错误类型</span>
          <Select
            placeholder="请选择错误类型..."
            className={styles['reason-form-class-select']}
            autoFocus
            value={state.selectedResult}
            mode="multiple"
            allowClear
            options={selectOptions}
            showSearch={false}
            onChange={onSelectChange}
            popupClassName={styles['reason-form-class-popup']}
            dropdownRender={(menu) => (
              <>
                {menu}
                <div className={styles['reason-form-class-tip']}>多选不可跨分类选择</div>
              </>
            )}
          />
        </div>
      );
    }

    return (
      <div className={styles['reason-form']}>
        {differentPart}
        <TextArea
          placeholder="请输入错误详情..."
          className={styles['comment-textarea']}
          maxLength={150}
          onChange={onContentChange}
          name="content"
          value={state.commentContent}
        />
      </div>
    );
  };

  return (
    <Modal
      className={styles.modal}
      title={renderTitle()}
      open={state.isOpen}
      onCancel={onClose}
      footer={renderFooter()}
    >
      {renderContent()}
    </Modal>
  );
};
